import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from kiteconnect import KiteConnect
import instructor
from openai import OpenAI
import os
from typing import Optional

# OpenAI setup
os.environ['OPENAI_API_KEY'] = "********************************************************************************************************************************************************************"
client = OpenAI()
client = instructor.patch(client)

# Kite API setup
kite_api_key = "eq5t4qqajzvgewyq"
kite_api_secret = "mqhu4z7mmemxtymd1dvuusiti8v8dpgb"

app = FastAPI()

origins = [
    "http://localhost",  # Covers cases where port is omitted
    "https://doq-module.netlify.app",
    "https://doqdevline.netlify.app",
    "https://a9b0-20-197-54-228.ngrok-free.app"  # Add your ngrok URL here
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins, but filter manually
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    response = await call_next(request)
    origin = request.headers.get("origin")
    if origin and (origin.startswith("http://localhost") or origin in origins):
        response.headers["Access-Control-Allow-Origin"] = origin
    return response

# Global variables to store session data
kite_instance = None
user_holdings = None

# Pydantic models


class LoginRequest(BaseModel):
    password: str


class TokenRequest(BaseModel):
    request_token: str


class ChatRequest(BaseModel):
    message: str


@app.get("/")
async def root():
    """Root endpoint for testing server availability."""
    return {"status": "ok", "message": "Trading Chatbot Server is running"}


@app.post("/login")
async def login(request: LoginRequest):
    """Authenticate user with hardcoded password."""
    if request.password == "1234":
        return {"success": True, "message": "Login successful"}
    else:
        raise HTTPException(status_code=401, detail="Invalid password")


@app.get("/kite-login-url")
async def get_kite_login_url():
    """Get Kite Connect login URL."""
    global kite_instance
    kite_instance = KiteConnect(api_key=kite_api_key)
    login_url = kite_instance.login_url()
    return {"login_url": login_url}


@app.get("/kite-token")
async def handle_kite_redirect(request: Request):
    """Handle Kite redirect and extract request token."""
    # This endpoint will be called by Kite after user login
    # The request token will be in the URL parameters
    request_token = request.query_params.get("request_token")
    status = request.query_params.get("status")

    if status == "success" and request_token:
        # Redirect to frontend with token
        return {"request_token": request_token, "status": "success"}
    else:
        raise HTTPException(
            status_code=400, detail="Kite login failed or was cancelled")


@app.post("/kite-connect")
async def connect_kite(request: TokenRequest):
    """Exchange request token for access token and fetch holdings."""
    global user_holdings

    kite_instance = KiteConnect(api_key=kite_api_key)

    if not kite_instance:
        raise HTTPException(
            status_code=400, detail="Kite instance not initialized. Please get login URL first.")

    try:
        # Generate session and set access token
        data = kite_instance.generate_session(
            request.request_token, api_secret=kite_api_secret)
        kite_instance.set_access_token(data["access_token"])

        # Fetch holdings
        user_holdings = kite_instance.positions()

        return {
            "success": True,
            "message": "Kite connected successfully",
            "holdings": user_holdings
        }
    except Exception as e:
        print(e)
        raise HTTPException(
            status_code=400, detail=f"Failed to connect to Kite: {str(e)}")


@app.get("/holdings")
async def get_holdings():
    """Get current holdings."""
    global user_holdings

    if not user_holdings:
        raise HTTPException(
            status_code=400, detail="No holdings data available. Please connect to Kite first.")

    return {"holdings": user_holdings}


@app.post("/chat")
async def chat_about_holdings(request: ChatRequest):
    """Chat about holdings using OpenAI."""
    global user_holdings

    try:
        response = client.chat.completions.create(
            model="o3-mini",
            messages=[
                {"role": "system", "content": "You're a knowledgeable finance assistant. Analyze the user's stock holdings and provide helpful insights, suggestions, and answers to their questions. Be concise but informative."},
                {"role": "user", "content": f"My current stock holdings are: {user_holdings}. User question: {request.message}"}
            ]
        )

        return {"response": response.choices[0].message.content.strip()}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get AI response: {str(e)}")


def start():
    uvicorn.run("server:app", host="0.0.0.0", port=8000, reload=True)


if __name__ == "__main__":
    start()
